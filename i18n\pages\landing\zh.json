{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "ShipAny", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/zh"}, "nav": {"items": [{"title": "功能特点", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "定价", "url": "/#pricing", "icon": "MdPayment"}]}, "buttons": [], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "几小时内快速构建 AI 创业项目，而不是几天", "highlight_text": "Ship Any", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板。<br/>通过丰富的模板和组件快速启动。", "show_happy_users": false, "show_badge": false}, "ai_generator": {"name": "ai-generator", "text_to_image": {"title": "文生图", "placeholder": "描述你想要生成的图片..."}, "image_to_image": {"title": "图生图", "upload_text": "点击上传图片"}, "translations": {"tabs": {"text_to_image": "文生图", "image_to_image": "图生图"}, "placeholders": {"negative_prompt": "描述你不想要在图片中出现的内容...", "image_modification": "描述你想要对图片进行的修改...", "file_support": "支持 JPG, PNG, WebP"}, "settings": {"ratio": "比例", "style": "风格", "color": "色彩", "lighting": "光影", "composition": "构图"}, "options": {"style": [{"zh": "无风格", "en": "No Style", "reference": "", "icon": "Circle"}, {"zh": "写实风", "en": "Semi-Realistic", "reference": "<PERSON><PERSON> (Netflix)", "icon": "Camera"}, {"zh": "极简扁平", "en": "Flat Minimal", "reference": "Adventure Time", "icon": "Square"}, {"zh": "超级英雄风", "en": "Comic Book", "reference": "Batman: The Animated Series", "icon": "Zap"}, {"zh": "青年热血动漫风", "en": "Shounen Style", "reference": "My Hero Academia", "icon": "Flame"}, {"zh": "可爱风", "en": "Moe Style", "reference": "K-On!", "icon": "Heart"}, {"zh": "暗黑成人风", "en": "Gritty Style", "reference": "Castlevania", "icon": "Moon"}, {"zh": "哥特风", "en": "Gothic Style", "reference": "Billy & Mandy", "icon": "Crown"}, {"zh": "脱线搞怪风", "en": "Absurdist", "reference": "<PERSON> and <PERSON><PERSON><PERSON>", "icon": "Smile"}, {"zh": "手绘感风", "en": "Sketchy Lines", "reference": "The Midnight Gospel", "icon": "Pencil"}, {"zh": "二头身Q版", "en": "Chibi Style", "reference": "Lucky Star", "icon": "Circle"}, {"zh": "超现实风", "en": "Surreal Style", "reference": "FLCL", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "黑白漫画风", "en": "Monochrome", "reference": "Samurai Jack", "icon": "Contrast"}, {"zh": "美式卡通", "en": "Classic Western", "reference": "<PERSON> and <PERSON>", "icon": "Star"}, {"zh": "高饱和亮色", "en": "Vibrant Colors", "reference": "<PERSON><PERSON><PERSON>", "icon": "Rainbow"}, {"zh": "2.5D分层", "en": "Cutout Style", "reference": "South Park", "icon": "Layers"}, {"zh": "青春柔光风", "en": "Soft Pastel", "reference": "Your Name", "icon": "Sun"}, {"zh": "抽象表现风", "en": "Experimental", "reference": "Pup<PERSON>", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "动态动感风", "en": "Kinetic Style", "reference": "Attack on Titan", "icon": "Wind"}, {"zh": "视觉小说风", "en": "Visual Novel", "reference": "Steins;Gate", "icon": "Book"}, {"zh": "复古赛璐璐手绘风", "en": "<PERSON><PERSON>", "reference": "<PERSON>", "icon": "Clock"}], "color": [{"zh": "无色彩", "en": "No Color", "reference": "", "icon": "Ban"}, {"zh": "高饱和鲜艳色", "en": "Vibrant Colors", "reference": "Kill la Kill", "icon": "Palette"}, {"zh": "柔和淡色系", "en": "<PERSON><PERSON>", "reference": "Your Name", "icon": "Flower"}, {"zh": "低饱和色", "en": "Muted Colors", "reference": "Death Note", "icon": "Moon"}, {"zh": "暗黑风格色调", "en": "Dark Palette", "reference": "Tokyo Ghoul", "icon": "Skull"}, {"zh": "复古色彩", "en": "Retro Colors", "reference": "<PERSON>", "icon": "Clock"}, {"zh": "冷色主导", "en": "<PERSON>", "reference": "Serial Experiments Lain", "icon": "Snowflake"}, {"zh": "暖色主导", "en": "<PERSON>nes", "reference": "Clannad", "icon": "Sun"}, {"zh": "单色调", "en": "Monochrome", "reference": "Samurai Jack", "icon": "Circle"}, {"zh": "高对比强明暗", "en": "High Contrast", "reference": "Attack on Titan", "icon": "Contrast"}, {"zh": "赛博霓虹色", "en": "Cyber Neon", "reference": "<PERSON>", "icon": "Zap"}, {"zh": "梦幻透明感色系", "en": "<PERSON><PERSON>", "reference": "The Garden of Words", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "自然写实色调", "en": "Natural Colors", "reference": "Spirited Away", "icon": "Mountain"}], "lighting": [{"zh": "无光影", "en": "No Light", "reference": "", "icon": "Ban"}, {"zh": "柔光", "en": "Soft Lighting", "reference": "Your Name", "icon": "Sun"}, {"zh": "高对比光", "en": "High Contrast Lighting", "reference": "Batman: <PERSON><PERSON>", "icon": "Contrast"}, {"zh": "逆光", "en": "Backlighting", "reference": "<PERSON>", "icon": "Sunrise"}, {"zh": "顶光", "en": "Top Lighting", "reference": "Death Note", "icon": "ArrowUp"}, {"zh": "底光", "en": "Under Lighting", "reference": "Mononoke", "icon": "ArrowDown"}, {"zh": "轮廓光", "en": "<PERSON><PERSON>", "reference": "Fate/stay night: UBW", "icon": "Circle"}, {"zh": "环境光", "en": "Ambient Lighting", "reference": "Spirited Away", "icon": "Globe"}, {"zh": "聚焦高亮", "en": "Spot Lighting", "reference": "<PERSON>b Psycho 100", "icon": "Flashlight"}, {"zh": "光斑", "en": "Light Leaks / Lens Flare", "reference": "The Garden of Words", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "赛博霓虹光", "en": "Neon Lighting", "reference": "Cyberpunk: Edgerunners", "icon": "Zap"}, {"zh": "黄金时段光", "en": "Golden Hour Lighting", "reference": "5 Centimeters per Second", "icon": "Sunset"}, {"zh": "月光", "en": "Moonlight / Cool Light", "reference": "Princess <PERSON><PERSON><PERSON>", "icon": "Moon"}], "composition": [{"zh": "无构图", "en": "No Composition", "reference": "", "icon": "Ban"}, {"zh": "三分法", "en": "Rule of Thirds", "reference": "Spirited Away", "icon": "Grid3X3"}, {"zh": "中心构图", "en": "Centered Composition", "reference": "Demon Slayer", "icon": "Target"}, {"zh": "对称构图", "en": "Symmetrical Composition", "reference": "<PERSON>", "icon": "FlipHorizontal"}, {"zh": "动态构图", "en": "Dynamic Composition", "reference": "Promare", "icon": "Wind"}, {"zh": "引导线构图", "en": "Leading Lines", "reference": "<PERSON>", "icon": "ArrowRight"}, {"zh": "负空间构图", "en": "Negative Space", "reference": "5 Centimeters per Second", "icon": "Square"}, {"zh": "框架内构图", "en": "Frame Within a Frame", "reference": "Paranoia Agent", "icon": "<PERSON>ame"}, {"zh": "鸟瞰角度", "en": "Bird's Eye View", "reference": "Attack on Titan", "icon": "Eye"}, {"zh": "低角度视角", "en": "Low Angle Shot", "reference": "Code Geass", "icon": "ArrowUp"}, {"zh": "第一人称视角", "en": "POV Composition", "reference": "Re:Zero", "icon": "User"}, {"zh": "极端特写", "en": "Extreme Close-Up", "reference": "Death Note", "icon": "ZoomIn"}, {"zh": "对角线构图", "en": "Diagonal Composition", "reference": "FLCL", "icon": "Slash"}, {"zh": "黄金比例构图", "en": "Golden Ratio Composition", "reference": "Princess <PERSON><PERSON><PERSON>", "icon": "RotateCcw"}, {"zh": "镜像构图", "en": "Reflection Composition", "reference": "<PERSON><PERSON><PERSON>", "icon": "FlipVertical"}]}, "controls": {"high_quality": "高质量", "generating": "生成中...", "generate": "生成图片"}}}, "gallery": {"title": "AI 生成作品展示", "subtitle": "探索使用我们平台创建的精彩 AI 生成艺术作品", "translations": {"title": "AI 生成作品展示", "subtitle": "探索精彩的 AI 生成艺术作品", "loading": "加载中...", "error": "图片加载失败", "retry": "重试", "close": "关闭", "image_details": "图片详情", "style": "风格", "color": "颜色", "lighting": "光影", "composition": "构图", "prompt": "提示词", "no_data": "暂无图片"}}, "introduce": {"name": "introduce", "title": "什么是 ShipAny", "label": "介绍", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，内置多种模板和组件。", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "即用型模板", "description": "从数十个生产就绪的 AI SaaS 模板中选择，快速启动您的项目。", "icon": "RiNextjsFill"}, {"title": "基础设施配置", "description": "立即获取内置最佳实践的可扩展基础设施。", "icon": "RiDatabase2Line"}, {"title": "快速部署", "description": "在几小时内将您的 AI SaaS 应用部署到生产环境，而不是几天。", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "为什么选择 ShipAny", "label": "优势", "description": "获取启动 AI 创业所需的一切 - 从即用型模板到技术支持。", "items": [{"title": "完整框架", "description": "基于 Next.js 构建，集成身份验证、支付和 AI 功能 - 一切开箱即用。", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "丰富的模板库", "description": "选择各种 AI SaaS 模板来启动您的项目 - 聊天机器人、图像生成等。", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "技术指导", "description": "获得专门支持并加入我们的开发者社区，确保您成功启动。", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "如何使用 ShipAny 启动项目", "description": "通过三个简单步骤启动您的 AI SaaS 创业项目：", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "获取 ShipAny", "description": "一次性付款购买 ShipAny。查收邮件获取代码和文档。", "image": {"src": "/imgs/features/5.png"}}, {"title": "开始您的项目", "description": "阅读文档并克隆 ShipAny 代码。开始构建您的 AI SaaS 创业项目。", "image": {"src": "/imgs/features/6.png"}}, {"title": "定制您的项目", "description": "使用您的数据和内容修改模板。满足特定的 AI 功能需求。", "image": {"src": "/imgs/features/7.png"}}, {"title": "部署到生产环境", "description": "通过几个步骤将项目部署到生产环境，立即开始服务客户。", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "ShipAny 核心功能", "description": "快速高效启动 AI SaaS 创业所需的一切。", "items": [{"title": "Next.js 模板", "description": "生产就绪的 Next.js 模板，支持 SEO 友好结构和国际化。", "icon": "RiNextjsFill"}, {"title": "身份验证和支付", "description": "集成 Google OAuth、一键登录和 Stripe 支付处理。", "icon": "RiKey2Fill"}, {"title": "数据基础设施", "description": "内置 Supabase 集成，提供可靠和可扩展的数据存储。", "icon": "RiDatabase2Line"}, {"title": "一键部署", "description": "无缝部署到 Vercel 或 Cloudflare，自动化设置。", "icon": "RiCloudy2Fill"}, {"title": "业务分析", "description": "集成 Google Analytics 和 Search Console 追踪增长。", "icon": "RiBarChart2Line"}, {"title": "AI 就绪基础设施", "description": "预配置 AI 集成，内置积分系统和 API 销售。", "icon": "RiRobot2Line"}]}, "showcase": {"name": "showcase", "title": "使用 ShipAny 构建的 AI SaaS 创业项目", "description": "易于使用，快速发布。", "items": [{"title": "ThinkAny", "description": "AI 搜索引擎", "url": "https://thinkany.ai", "target": "_blank", "image": {"src": "/imgs/showcases/7.png"}}, {"title": "HeyBeauty", "description": "AI 虚拟试妆", "url": "https://heybeauty.ai", "target": "_blank", "image": {"src": "/imgs/showcases/5.png"}}, {"title": "AI Wallpaper", "description": "AI 壁纸生成器", "url": "https://aiwallpaper.shop", "target": "_blank", "image": {"src": "/imgs/showcases/1.png"}}, {"title": "AI Cover", "description": "AI 封面生成器", "url": "https://aicover.design", "target": "_blank", "image": {"src": "/imgs/showcases/2.png"}}, {"title": "GPTs Works", "description": "GPTs 目录", "url": "https://gpts.works", "target": "_blank", "image": {"src": "/imgs/showcases/3.png"}}, {"title": "Melod<PERSON>", "description": "AI 音乐播放器", "url": "https://melodis.co", "target": "_blank", "image": {"src": "/imgs/showcases/4.png"}}, {"title": "<PERSON><PERSON>", "description": "AI 落地页生成器", "url": "https://pagen.so", "target": "_blank", "image": {"src": "/imgs/showcases/6.png"}}, {"title": "SoraFM", "description": "AI 视频生成器", "url": "https://sorafm.trys.ai", "target": "_blank", "image": {"src": "/imgs/showcases/8.png"}}, {"title": "PodLM", "description": "AI 播客生成器", "url": "https://podlm.ai", "target": "_blank", "image": {"src": "/imgs/showcases/9.png"}}]}, "stats": {"name": "stats", "label": "统计", "title": "用户喜爱 ShipAny", "description": "因为它易于使用且快速发布。", "icon": "FaRegHeart", "items": [{"title": "信任", "label": "99+", "description": "客户"}, {"title": "内置", "label": "20+", "description": "组件"}, {"title": "快速发布", "label": "5", "description": "分钟"}]}, "pricing": {"name": "pricing", "label": "定价", "title": "定价", "description": "获取 ShipAny 的所有功能，快速启动您的 AI SaaS 创业项目。", "groups": [], "items": [{"title": "入门版", "description": "开始您的第一个 SaaS 创业项目。", "features_title": "包含", "features": ["100 积分，有效期 1 个月", "NextJS 模板", "SEO 友好结构", "Stripe 支付", "Supabase 数据存储", "Google OAuth 和一键登录", "国际化支持"], "interval": "one-time", "amount": 9900, "cn_amount": 69900, "currency": "USD", "price": "$99", "original_price": "$199", "unit": "USD", "is_featured": false, "tip": "一次付费，无限项目！", "button": {"title": "获取 ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "starter", "product_name": "ShipAny 模板入门版", "credits": 100, "valid_months": 1}, {"title": "标准版", "description": "快速启动您的 SaaS 创业项目。", "label": "热门", "features_title": "包含入门版所有功能，另加", "features": ["200 积分，有效期 3 个月", "Vercel 或 Cloudflare 部署", "隐私和条款生成", "Google Analytics 集成", "Google Search Console 集成", "Discord 社区", "首次发布技术支持", "终身更新"], "interval": "one-time", "amount": 19900, "cn_amount": 139900, "currency": "USD", "price": "$199", "original_price": "$299", "unit": "USD", "is_featured": true, "tip": "一次付费，无限项目！", "button": {"title": "获取 ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "standard", "product_name": "ShipAny 模板标准版", "credits": 200, "valid_months": 3}, {"title": "高级版", "description": "构建任何 AI SaaS 创业项目。", "features_title": "包含标准版所有功能，另加", "features": ["300 积分，有效期 1 年", "AI 业务功能", "用户中心", "积分系统", "SaaS API 销售", "管理系统", "优先技术支持"], "interval": "one-time", "amount": 29900, "cn_amount": 199900, "currency": "USD", "price": "$299", "original_price": "$399", "unit": "USD", "is_featured": false, "tip": "一次付费，无限项目！", "button": {"title": "获取 ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "premium", "product_name": "ShipAny 模板高级版", "credits": 300, "valid_months": 12}]}, "testimonial": {"name": "testimonial", "label": "用户评价", "title": "用户如何评价 ShipAny", "description": "听听使用 ShipAny 启动 AI 创业项目的开发者和创始人怎么说。", "icon": "GoThumbsup", "items": [{"title": "陈大卫", "label": "AIWallpaper.shop 创始人", "description": "ShipAny 为我们节省了数月的开发时间。我们仅用 2 天就启动了 AI 壁纸业务，一周内就获得了第一个付费客户！", "image": {"src": "/imgs/users/1.png"}}, {"title": "金瑞秋", "label": "HeyBeauty.ai 技术总监", "description": "预构建的 AI 基础设施是一个游戏规则改变者。我们无需担心架构 - 只需专注于 AI 美容技术并快速上线。", "image": {"src": "/imgs/users/2.png"}}, {"title": "马库斯", "label": "独立开发者", "description": "作为独立开发者，ShipAny 给了我所需的一切 - 身份验证、支付、AI 集成和漂亮的 UI。一个周末就启动了我的 SaaS！", "image": {"src": "/imgs/users/3.png"}}, {"title": "索菲亚", "label": "Melodisco CEO", "description": "这些模板可直接用于生产且高度可定制。我们用几小时而不是几个月就建立了 AI 音乐平台。上市时间令人难以置信！", "image": {"src": "/imgs/users/4.png"}}, {"title": "詹姆斯", "label": "GPTs.works 技术主管", "description": "ShipAny 的基础设施非常稳固。我们从 0 扩展到 1 万用户都没碰后端。这是我们 AI 创业最好的投资。", "image": {"src": "/imgs/users/5.png"}}, {"title": "张安娜", "label": "创业者", "description": "从想法到上线只用了 3 天！ShipAny 的模板和部署工具让我们能够以令人难以置信的速度测试 AI 业务概念。", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于 ShipAny 的常见问题", "description": "还有其他问题？通过 Discord 或电子邮件联系我们。", "groups": [{"id": "product", "title": "产品与功能", "description": "了解 ShipAny 的核心功能和特性", "items": [{"title": "ShipAny 究竟是什么，它是如何工作的？", "description": "ShipAny 是一个专门为构建 AI SaaS 创业项目设计的综合性 NextJS 模板。它提供即用型模板、基础设施设置和部署工具，帮助您在几小时内而不是几天内启动 AI 业务。"}, {"title": "我可以用 ShipAny 构建什么类型的 AI SaaS？", "description": "ShipAny 支持广泛的 AI 应用，从内容生成到数据分析工具。我们的模板涵盖流行用例，如 AI 聊天机器人、内容生成器、图像处理应用等。"}, {"title": "ShipAny 的基础设施包括什么？", "description": "ShipAny 提供完整的基础设施栈，包括身份验证、数据库设置、API 集成、支付处理和可扩展的云部署。一切都按照行业最佳实践预先配置。"}]}, {"id": "technical", "title": "技术与开发", "description": "技术要求和开发流程相关问题", "items": [{"title": "使用 ShipAny 需要高级技术技能吗？", "description": "虽然基本的编程知识会有帮助，但 ShipAny 设计得非常开发者友好。我们的模板和文档使您即使不是 AI 或云基础设施专家也能轻松入门。"}, {"title": "使用 ShipAny 通常需要多长时间才能启动？", "description": "使用 ShipAny，您可以在几小时内完成工作原型，并在几小时内完成生产就绪的应用。我们的一键部署和预配置基础设施显著缩短了传统的数月开发周期。"}, {"title": "我可以自定义模板以匹配我的品牌吗？", "description": "当然可以！所有 ShipAny 模板都完全可定制。您可以修改设计、功能和功能性以匹配您的品牌标识和特定业务需求，同时保持强大的底层基础设施。"}]}]}, "cta": {"name": "cta", "title": "启动您的第一个 AI SaaS 创业项目", "description": "从这里开始，使用 ShipAny 启动。", "buttons": [{"title": "获取 ShipAny", "url": "https://shipany.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "阅读文档", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板。通过丰富的模板和组件快速启动。", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "copyright": "© 2025 • ShipAny 保留所有权利。", "nav": {"items": [{"title": "关于", "children": [{"title": "功能特点", "url": "/#feature", "target": "_self"}, {"title": "案例展示", "url": "/#showcase", "target": "_self"}, {"title": "定价", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "组件", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "模板", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "友情链接", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}