"use client";

import React, { useState } from "react";
import { AIGenerator as AIGeneratorType } from "@/types/blocks/ai-generator";
import TabSwitcher from "./TabSwitcher";
import InputSection from "./InputSection";
import ControlsSection from "./ControlsSection";
import ImageResultsDisplay, { useImageResultsManager } from "../image-results";
import { GenerationSettings } from "@/types/image-results";

export default function AIGenerator({ generator }: { generator: AIGeneratorType }) {
  const [textPrompt, setTextPrompt] = useState("");
  const [imagePrompt, setImagePrompt] = useState("");
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState("text-to-image");
  const [isAnimating, setIsAnimating] = useState(false);

  // 新增开关状态
  const [enableHighQuality, setEnableHighQuality] = useState(false);
  const [imageEnableHighQuality, setImageEnableHighQuality] = useState(false);

  // 图片结果管理器
  const imageResultsManager = useImageResultsManager({
    config: {
      enableCountdown: true,
      countdownDuration: 10,
      maxRetries: 3,
      retryDelay: 5,
    },
  });

  // 获取翻译数据
  const translations = generator.translations;

  // 处理风格选项 - 支持新的数据结构
  const getStyleOptions = () => {
    const styleOptions = translations?.options?.style;
    if (styleOptions && Array.isArray(styleOptions) && styleOptions.length > 0) {
      // 检查是否是新的对象格式
      if (typeof styleOptions[0] === 'object' && styleOptions[0].zh && styleOptions[0].en) {
        return styleOptions;
      }
      // 兼容旧的字符串数组格式
      return styleOptions.map((style: string) => ({ zh: style, en: style, reference: "" }));
    }
    // 默认风格选项
    return [
      { zh: "无风格", en: "No Style", reference: "" },
      { zh: "写实风", en: "Semi-Realistic", reference: "Arcane (Netflix)" },
      { zh: "极简扁平", en: "Flat Minimal", reference: "Adventure Time" },
      { zh: "超级英雄风", en: "Comic Book", reference: "Batman: The Animated Series" },
      { zh: "青年热血动漫风", en: "Shounen Style", reference: "My Hero Academia" },
      { zh: "萝莉/可爱风", en: "Moe Style", reference: "K-On!" },
      { zh: "暗黑成人风", en: "Gritty Style", reference: "Invincible, Castlevania" },
      { zh: "哥特风", en: "Gothic Style", reference: "The Grim Adventures of Billy & Mandy" },
      { zh: "脱线搞怪风", en: "Absurdist", reference: "Rick and Morty" },
      { zh: "彩铅/手绘感风", en: "Sketchy Lines", reference: "The Midnight Gospel" },
      { zh: "二头身Q版", en: "Chibi Style", reference: "Lucky Star" },
      { zh: "超现实风", en: "Surreal Style", reference: "FLCL (Fooly Cooly)" },
      { zh: "黑白漫画风", en: "Monochrome", reference: "Samurai Jack" },
      { zh: "美式卡通", en: "Classic Western", reference: "Looney Tunes, Tom and Jerry" },
      { zh: "高饱和亮色", en: "Vibrant Colors", reference: "The Amazing World of Gumball" },
      { zh: "假3D/分层2.5D", en: "Cutout Style", reference: "South Park" },
      { zh: "青春柔光风", en: "Soft Pastel", reference: "Your Name" },
      { zh: "抽象表现风", en: "Experimental", reference: "Puparia" },
      { zh: "动态动感风", en: "Kinetic Style", reference: "Attack on Titan" },
      { zh: "视觉小说风", en: "Visual Novel", reference: "Steins;Gate" },
      { zh: "复古赛璐璐手绘风", en: "Retro Cel", reference: "Cowboy Bebop" }
    ];
  };

  // 处理色彩选项 - 支持新的数据结构
  const getColorOptions = () => {
    const colorOptions = translations?.options?.color;
    if (colorOptions && Array.isArray(colorOptions) && colorOptions.length > 0) {
      // 检查是否是新的对象格式
      if (typeof colorOptions[0] === 'object' && colorOptions[0].zh && colorOptions[0].en) {
        return colorOptions;
      }
      // 兼容旧的字符串数组格式
      return colorOptions.map((color: string) => ({ zh: color, en: color, reference: "" }));
    }
    // 默认色彩选项
    return [
      { zh: "无色彩", en: "No Color", reference: "" },
      { zh: "高饱和鲜艳色", en: "Vibrant Colors", reference: "Kill la Kill, Gumball" },
      { zh: "柔和淡色系", en: "Pastel Tones", reference: "Your Name, K-On!" },
      { zh: "暗色调/低饱和色", en: "Muted Colors", reference: "Death Note, Ergo Proxy" },
      { zh: "暗黑风格色调", en: "Dark Palette", reference: "Castlevania, Tokyo Ghoul" },
      { zh: "复古色彩", en: "Retro Colors", reference: "Cowboy Bebop, FLCL" },
      { zh: "冷色主导", en: "Cool Tones", reference: "Serial Experiments Lain" },
      { zh: "暖色主导", en: "Warm Tones", reference: "Clannad, Wolf Children" },
      { zh: "单色调", en: "Monochrome", reference: "Samurai Jack" },
      { zh: "高对比强明暗", en: "High Contrast", reference: "Attack on Titan, Promare" },
      { zh: "赛博霓虹色", en: "Cyber Neon", reference: "Akira, Cyberpunk: Edgerunners" },
      { zh: "梦幻透明感色系", en: "Dreamy Glow", reference: "The Garden of Words" },
      { zh: "自然写实色调", en: "Natural Colors", reference: "Whisper of the Heart, Spirited Away" }
    ];
  };

  // 处理光照选项 - 支持新的数据结构
  const getLightingOptions = () => {
    const lightingOptions = translations?.options?.lighting;
    if (lightingOptions && Array.isArray(lightingOptions) && lightingOptions.length > 0) {
      // 检查是否是新的对象格式
      if (typeof lightingOptions[0] === 'object' && lightingOptions[0].zh && lightingOptions[0].en) {
        return lightingOptions;
      }
      // 兼容旧的字符串数组格式
      return lightingOptions.map((lighting: string) => ({ zh: lighting, en: lighting, reference: "" }));
    }
    // 默认光照选项
    return [
      { zh: "无光影", en: "No Light", reference: "" },
      { zh: "柔光", en: "Soft Lighting", reference: "Your Name, Clannad" },
      { zh: "强光/高对比光", en: "High Contrast Lighting", reference: "Attack on Titan, Batman: TAS" },
      { zh: "逆光", en: "Backlighting", reference: "Violet Evergarden, Demon Slayer" },
      { zh: "顶光/天光", en: "Top Lighting", reference: "Death Note, Psycho-Pass" },
      { zh: "底光", en: "Under Lighting", reference: "Mononoke, Hellsing" },
      { zh: "轮廓光/边缘光", en: "Rim Lighting", reference: "Fate/stay night: UBW" },
      { zh: "环境光", en: "Ambient Lighting", reference: "Spirited Away, Whisper of the Heart" },
      { zh: "闪光/聚焦高亮", en: "Spot Lighting", reference: "Mob Psycho 100, One Punch Man" },
      { zh: "光斑/光泄", en: "Light Leaks / Lens Flare", reference: "The Garden of Words, Evangelion" },
      { zh: "赛博霓虹光", en: "Neon Lighting", reference: "Cyberpunk: Edgerunners, Akira" },
      { zh: "黄金时段光", en: "Golden Hour Lighting", reference: "5 Centimeters per Second" },
      { zh: "月光/冷光", en: "Moonlight / Cool Light", reference: "Princess Mononoke, Bleach" }
    ];
  };

  // 处理构图选项 - 支持新的数据结构
  const getCompositionOptions = () => {
    const compositionOptions = translations?.options?.composition;
    if (compositionOptions && Array.isArray(compositionOptions) && compositionOptions.length > 0) {
      // 检查是否是新的对象格式
      if (typeof compositionOptions[0] === 'object' && compositionOptions[0].zh && compositionOptions[0].en) {
        return compositionOptions;
      }
      // 兼容旧的字符串数组格式
      return compositionOptions.map((composition: string) => ({ zh: composition, en: composition, reference: "" }));
    }
    // 默认构图选项
    return [
      { zh: "无构图", en: "No Composition", reference: "" },
      { zh: "三分法", en: "Rule of Thirds", reference: "Naruto, Spirited Away" },
      { zh: "中心构图", en: "Centered Composition", reference: "Demon Slayer, Attack on Titan" },
      { zh: "对称构图", en: "Symmetrical Composition", reference: "Violet Evergarden, Mononoke" },
      { zh: "动态构图", en: "Dynamic Composition", reference: "My Hero Academia, Promare" },
      { zh: "引导线构图", en: "Leading Lines", reference: "Akira, Ghost in the Shell" },
      { zh: "负空间构图", en: "Negative Space", reference: "5 Centimeters per Second, Erased" },
      { zh: "框架内构图", en: "Frame Within a Frame", reference: "Paranoia Agent, Psycho-Pass" },
      { zh: "鸟瞰角度", en: "Bird's Eye View", reference: "Attack on Titan, Chainsaw Man" },
      { zh: "低角度视角", en: "Low Angle Shot", reference: "One Punch Man, Code Geass" },
      { zh: "第一人称视角", en: "POV Composition", reference: "Re:Zero, Sword Art Online" },
      { zh: "极端特写", en: "Extreme Close-Up", reference: "Death Note, Jujutsu Kaisen" },
      { zh: "对角线构图", en: "Diagonal Composition", reference: "Trigun Stampede, FLCL" },
      { zh: "黄金比例构图", en: "Golden Ratio Composition", reference: "Princess Mononoke, Nausicaä" },
      { zh: "镜像构图", en: "Reflection Composition", reference: "The Garden of Words, Paprika" }
    ];
  };

  // 设置选项数据 - 使用翻译或回退到硬编码
  const settingsOptions = {
    ratio: ["1:1", "16:9", "9:16", "4:3", "3:4", "2:3", "3:2"],
    style: getStyleOptions(),
    color: getColorOptions(),
    lighting: getLightingOptions(),
    composition: getCompositionOptions()
  };

  // 获取选项的显示文本（通用函数）
  const getOptionDisplayText = (option: any, locale: string = 'zh') => {
    if (typeof option === 'object' && option.zh && option.en) {
      return locale === 'en' ? option.en : option.zh;
    }
    return option;
  };

  // 获取当前语言环境
  const getCurrentLocale = () => {
    return translations?.settings?.style === 'Style' ? 'en' : 'zh';
  };

  // 设置状态 - 使用选项数组中的第一个选项作为默认值
  const [settings, setSettings] = useState({
    ratio: settingsOptions.ratio[0],
    style: getOptionDisplayText(settingsOptions.style[0], getCurrentLocale()),
    color: getOptionDisplayText(settingsOptions.color[0], getCurrentLocale()),
    lighting: getOptionDisplayText(settingsOptions.lighting[0], getCurrentLocale()),
    composition: getOptionDisplayText(settingsOptions.composition[0], getCurrentLocale())
  });

  if (generator.disabled) {
    return null;
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerate = async () => {
    const currentPrompt = activeTab === "text-to-image" ? textPrompt : imagePrompt;
    const currentMode = activeTab as 'text-to-image' | 'image-to-image';

    if (!currentPrompt.trim()) return;
    if (currentMode === 'image-to-image' && !uploadedImage) return;

    // 构建生成设置
    const generationSettings: GenerationSettings = {
      ratio: settings.ratio,
      style: settings.style,
      color: settings.color,
      lighting: settings.lighting,
      composition: settings.composition,
      enableHighQuality: currentMode === 'text-to-image' ? enableHighQuality : imageEnableHighQuality,
      provider: 'openai', // 可以根据需要配置
      model: 'dall-e-3', // 可以根据需要配置
    };

    // 使用图片结果管理器生成图片
    await imageResultsManager.generateImage(
      currentPrompt,
      generationSettings,
      currentMode,
      currentMode === 'image-to-image' ? uploadedImage : undefined
    );

    // 清空输入（可选）
    if (currentMode === 'text-to-image') {
      setTextPrompt("");
    } else {
      setImagePrompt("");
      setUploadedImage(null);
    }
  };

  const handleTabChange = (newTab: string) => {
    if (newTab === activeTab || isAnimating) return;

    setIsAnimating(true);

    // 立即切换activeTab以触发动画
    setActiveTab(newTab);

    // 动画完成后重置所有状态
    setTimeout(() => {
      // 重置所有设置到默认值（使用选项数组中的第一个选项）
      setSettings({
        ratio: settingsOptions.ratio[0],
        style: getOptionDisplayText(settingsOptions.style[0], getCurrentLocale()),
        color: getOptionDisplayText(settingsOptions.color[0], getCurrentLocale()),
        lighting: getOptionDisplayText(settingsOptions.lighting[0], getCurrentLocale()),
        composition: getOptionDisplayText(settingsOptions.composition[0], getCurrentLocale())
      });

      // 重置其他状态
      setTextPrompt("");
      setImagePrompt("");
      setUploadedImage(null);
      setGeneratedImage(null);
      setEnableHighQuality(false);
      setImageEnableHighQuality(false);

      setIsAnimating(false);
    }, 300);
  };



  return (
    <section id={generator.name} className="py-16">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          <div className="tab-switcher-container mb-8">
            <TabSwitcher
              activeTab={activeTab}
              isAnimating={isAnimating}
              onTabChange={handleTabChange}
              translations={translations}
            />

            <div className="mt-8 relative overflow-hidden">
              <div
                className={`flex transition-transform duration-300 ease-in-out will-change-transform ${
                  activeTab === "text-to-image" ? "translate-x-0" : "-translate-x-1/2"
                }`}
                style={{
                  width: "200%",
                  backfaceVisibility: "hidden",
                  transform: activeTab === "text-to-image" ? "translateX(0)" : "translateX(-50%)"
                }}
              >
                {/* Text to Image Panel */}
                <div className="w-1/2 flex-shrink-0">
                  <div className="main-content-container border border-white/20 rounded-2xl p-0 overflow-hidden bg-black/20 backdrop-blur-md">
                    <InputSection
                      mode="text-to-image"
                      textPrompt={textPrompt}
                      setTextPrompt={setTextPrompt}
                      generator={generator}
                      imagePrompt=""
                      setImagePrompt={() => {}}
                      uploadedImage={null}
                      handleImageUpload={() => {}}
                      translations={translations}
                    />
                    <ControlsSection
                      mode="text-to-image"
                      activeTab={activeTab}
                      settings={settings}
                      setSettings={setSettings}
                      settingsOptions={settingsOptions}
                      enableHighQuality={enableHighQuality}
                      setEnableHighQuality={setEnableHighQuality}
                      textPrompt={textPrompt}
                      setTextPrompt={setTextPrompt}
                      imageEnableHighQuality={false}
                      setImageEnableHighQuality={() => {}}
                      uploadedImage={null}
                      setUploadedImage={() => {}}
                      imagePrompt=""
                      setImagePrompt={() => {}}
                      isGenerating={imageResultsManager.isProcessing}
                      handleGenerate={handleGenerate}
                      translations={translations}
                    />
                  </div>
                </div>

                {/* Image to Image Panel */}
                <div className="w-1/2 flex-shrink-0">
                  <div className="main-content-container border border-white/20 rounded-2xl p-0 overflow-hidden bg-black/20 backdrop-blur-md">
                    <InputSection
                      mode="image-to-image"
                      textPrompt=""
                      setTextPrompt={() => {}}
                      generator={generator}
                      imagePrompt={imagePrompt}
                      setImagePrompt={setImagePrompt}
                      uploadedImage={uploadedImage}
                      handleImageUpload={handleImageUpload}
                      translations={translations}
                    />
                    <ControlsSection
                      mode="image-to-image"
                      activeTab={activeTab}
                      settings={settings}
                      setSettings={setSettings}
                      settingsOptions={settingsOptions}
                      enableHighQuality={false}
                      setEnableHighQuality={() => {}}
                      textPrompt=""
                      setTextPrompt={setTextPrompt}
                      imageEnableHighQuality={imageEnableHighQuality}
                      setImageEnableHighQuality={setImageEnableHighQuality}
                      uploadedImage={uploadedImage}
                      setUploadedImage={setUploadedImage}
                      imagePrompt={imagePrompt}
                      setImagePrompt={setImagePrompt}
                      isGenerating={imageResultsManager.isProcessing}
                      handleGenerate={handleGenerate}
                      translations={translations}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 图片结果展示区域 */}
          <div className="mt-8">
            <ImageResultsDisplay
              results={imageResultsManager.results}
              config={{
                enableCountdown: true,
                countdownDuration: 10,
                maxRetries: 3,
                retryDelay: 5,
              }}
              onCancel={imageResultsManager.cancelGeneration}
            />
          </div>
        </div>
      </div>


    </section>
  );
}