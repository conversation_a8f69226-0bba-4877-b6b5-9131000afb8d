"use client";

import { useState } from "react";
import { GenerationResult, ImageData } from "@/types/image-results";
import ImageGrid from "./ImageGrid";
import ImagePreviewModal from "./ImagePreviewModal";
import GenerationProgress from "./GenerationProgress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Settings, Type, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageResultContainerProps {
  result: GenerationResult;
  className?: string;
  enableCountdown?: boolean;
  onCancel?: () => void;
}

export default function ImageResultContainer({
  result,
  className,
  enableCountdown = true,
  onCancel,
}: ImageResultContainerProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);

  const handleImageClick = (image: ImageData, index: number) => {
    setPreviewIndex(index);
    setPreviewOpen(true);
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(timestamp);
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const getStatusBadge = () => {
    switch (result.status) {
      case 'waiting':
        return <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300">等待中</Badge>;
      case 'generating':
        return <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">生成中</Badge>;
      case 'completed':
        return <Badge variant="secondary" className="bg-green-500/20 text-green-300">已完成</Badge>;
      case 'failed':
        return <Badge variant="destructive" className="bg-red-500/20 text-red-300">生成失败</Badge>;
      default:
        return null;
    }
  };



  return (
    <div className={cn(
      "w-full border border-white/20 rounded-2xl overflow-hidden bg-black/40 backdrop-blur-xl",
      className
    )}>
      {/* Header */}
      <div className="p-4 border-b border-white/20 bg-black/10">
        <div className="flex items-start justify-between gap-4">
          {/* Left side - Generation info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {result.mode === 'text-to-image' ? (
                <Type className="w-4 h-4 text-white/70" />
              ) : (
                <ImageIcon className="w-4 h-4 text-white/70" />
              )}
              <span className="text-white/70 text-sm">
                {result.mode === 'text-to-image' ? '文本生成图片' : '图片转图片'}
              </span>
              {getStatusBadge()}
            </div>
            
            <div className="space-y-1">
              <p className="text-white text-sm font-medium">
                {truncateText(result.prompt)}
              </p>
              
              {/* Settings display */}
              {Object.keys(result.settings).length > 0 && (
                <div className="flex items-center gap-2 text-white/60 text-xs">
                  <Settings className="w-3 h-3" />
                  <span>
                    {result.settings.provider && `${result.settings.provider} • `}
                    {result.settings.ratio && `${result.settings.ratio} • `}
                    {result.settings.style && `${result.settings.style} • `}
                    {result.settings.enableHighQuality && '高质量'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Timestamp and actions */}
          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center gap-1 text-white/60 text-xs">
              <Calendar className="w-3 h-3" />
              <span>{formatTimestamp(result.timestamp)}</span>
            </div>
            

          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Show progress for generating status */}
        {(result.status === 'waiting' || result.status === 'generating') && (
          <div className="grid grid-cols-4 gap-3 w-full">
            {/* 显示4个固定位置的图片骨架 */}
            {[0, 1, 2, 3].map((position) => (
              <div
                key={position}
                className="relative overflow-hidden rounded-lg aspect-square bg-gradient-to-br from-gray-700 to-gray-800 border border-white/20"
              >
                {/* 加载动画 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                </div>

                {/* 骨架图案 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10 animate-pulse" />

                {/* 位置指示器 */}
                <div className="absolute top-2 left-2 bg-gray-600/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded">
                  {position + 1}
                </div>

                {/* 状态指示器 */}
                <div className="absolute bottom-2 right-2 bg-blue-500/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded">
                  {result.status === 'waiting' ? '等待中' : '生成中'}
                </div>

                {/* 波纹效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/20 to-transparent transform -skew-x-12 animate-pulse"
                     style={{ animationDelay: `${position * 0.2}s` }} />
              </div>
            ))}
          </div>
        )}

        {/* Show error for failed status */}
        {result.status === 'failed' && (
          <div className="text-center py-8">
            <div className="text-red-400 text-4xl mb-2">⚠️</div>
            <h3 className="text-white font-medium mb-1">生成失败</h3>
            <p className="text-white/60 text-sm">
              {result.error || '图片生成过程中出现错误，请重试'}
            </p>
          </div>
        )}

        {/* Show images for completed status */}
        {result.status === 'completed' && result.images.length > 0 && (
          <ImageGrid
            images={result.images}
            onImageClick={handleImageClick}
          />
        )}


      </div>

      {/* Preview Modal */}
      {result.images.length > 0 && (
        <ImagePreviewModal
          isOpen={previewOpen}
          onClose={() => setPreviewOpen(false)}
          images={result.images}
          currentIndex={previewIndex}
          onIndexChange={setPreviewIndex}
        />
      )}
    </div>
  );
}
