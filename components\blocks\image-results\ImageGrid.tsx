"use client";

import { useState } from "react";
import { ImageData } from "@/types/image-results";
import { cn } from "@/lib/utils";

interface ImageGridProps {
  images: ImageData[];
  onImageClick: (image: ImageData, index: number) => void;
  className?: string;
}

export default function ImageGrid({ images, onImageClick, className }: ImageGridProps) {
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());
  const [errorImages, setErrorImages] = useState<Set<number>>(new Set());

  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => new Set([...prev, index]));
  };

  const handleImageError = (index: number) => {
    setErrorImages(prev => new Set([...prev, index]));
  };

  return (
    <div className={cn("grid grid-cols-4 gap-3 w-full", className)}>
      {/* 渲染4个固定位置 */}
      {[0, 1, 2, 3].map((position) => {
        const image = images[position]; // 获取对应位置的图片
        const hasImage = image !== undefined;

        return (
          <div
            key={position}
            className={cn(
              "relative overflow-hidden rounded-lg transition-all duration-300",
              hasImage ? "cursor-pointer group hover:scale-[1.02] hover:shadow-lg aspect-square" : ""
            )}
            onClick={hasImage ? () => onImageClick(image, position) : undefined}
          >
            {hasImage ? (
              <>
                {/* Loading placeholder */}
                {!loadedImages.has(position) && !errorImages.has(position) && (
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
                    <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  </div>
                )}

                {/* Error state */}
                {errorImages.has(position) && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center">
                    <div className="text-center text-red-600">
                      <div className="text-2xl mb-2">⚠️</div>
                      <div className="text-sm">加载失败</div>
                    </div>
                  </div>
                )}

                {/* Image */}
                <img
                  src={image.url}
                  alt={`Generated image ${position + 1}`}
                  className={cn(
                    "w-full h-full object-contain transition-all duration-500",
                    loadedImages.has(position) ? "opacity-100" : "opacity-0"
                  )}
                  onLoad={() => handleImageLoad(position)}
                  onError={() => handleImageError(position)}
                  loading="lazy"
                />

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                      <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Image info badge */}
                <div className="absolute bottom-2 right-2 bg-black/70 backdrop-blur-sm text-white text-xs px-2 py-1 rounded">
                  {image.provider}
                </div>
              </>
            ) : (
              /* 空位置不显示任何内容 */
              null
            )}
          </div>
        );
      })}
    </div>
  );
}
