export interface ImageData {
  url: string;
  location: string;
  bucket: string;
  key: string;
  filename: string;
  provider: string;
}

export interface GenerationSettings {
  ratio?: string;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  enableHighQuality?: boolean;
  provider?: string;
  model?: string;
}

export interface GenerationResult {
  id: string;
  timestamp: Date;
  prompt: string;
  settings: GenerationSettings;
  images: ImageData[];
  status: 'waiting' | 'generating' | 'completed' | 'failed';
  error?: string;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string; // For image-to-image mode
}

export interface GenerationQueue {
  id: string;
  prompt: string;
  settings: GenerationSettings;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string;
  countdown?: number;
}

export interface ImageResultsConfig {
  enableCountdown?: boolean;
  countdownDuration?: number; // in seconds
  maxRetries?: number;
  retryDelay?: number; // in seconds
}
